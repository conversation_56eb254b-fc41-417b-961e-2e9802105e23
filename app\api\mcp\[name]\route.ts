import { NextRequest } from 'next/server';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { SseServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import fs from 'node:fs/promises';
import path from 'node:path';

// Create a simple fallback server for when implementations are missing
function createFallbackServer(name: string, error: string) {
  const server = new McpServer({
    name: `${name} (Fallback)`,
    version: '1.0.0',
  });
  
  server.tool(
    'error_info',
    'Get information about the error',
    {},
    async () => {
      return {
        content: [
          {
            type: 'text',
            text: `Error initializing MCP server "${name}": ${error}`,
          },
        ],
      };
    }
  );
  
  return server;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { name: string } }
) {
  const name = params.name;
  
  try {
    // Load MCP server configuration
    const configPath = path.resolve(process.cwd(), './mcp-servers.json');
    const configData = await fs.readFile(configPath, 'utf-8');
    const config = JSON.parse(configData);
    const serverConfig = config.mcpServers[name];
    
    if (!serverConfig) {
      return new Response(`MCP server "${name}" not found`, { status: 404 });
    }
    
    let server: McpServer;
    
    // Try to load the server implementation
    try {
      switch (name) {
        case 'supabase-new':
          // Dynamic import with error handling
          try {
            const { createClient } = await import('@supabase/supabase-js');
            
            server = new McpServer({
              name: 'Supabase API',
              version: '1.0.0',
            });
            
            // Supabase configuration from environment
            const projectRef = serverConfig.env.SUPABASE_PROJECT_REF;
            const supabaseUrl = `https://${projectRef}.supabase.co`;
            const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || serverConfig.env.SUPABASE_DB_PASSWORD;
            
            const supabase = createClient(supabaseUrl, supabaseKey);
            
            // Add a simple query tool
            server.tool(
              'supabase_query',
              'Execute a Supabase query',
              {
                table: { type: 'string', description: 'The table name' },
                select: { type: 'string', description: 'Columns to select' },
                limit: { type: 'number', description: 'Maximum number of results', optional: true }
              },
              async ({ table, select, limit = 10 }) => {
                try {
                  const { data, error } = await supabase
                    .from(table)
                    .select(select)
                    .limit(limit);
                  
                  if (error) throw error;
                  
                  return {
                    content: [
                      {
                        type: 'text',
                        text: `Found ${data.length} records`,
                      },
                      {
                        type: 'json',
                        json: JSON.stringify(data),
                      },
                    ],
                  };
                } catch (error) {
                  return {
                    content: [
                      {
                        type: 'text',
                        text: `Error executing Supabase query: ${error.message}`,
                      },
                    ],
                  };
                }
              }
            );
          } catch (error) {
            console.error('Failed to initialize Supabase server:', error);
            server = createFallbackServer(name, error.message);
          }
          break;
          
        case 'mongodb':
          // Dynamic import with error handling
          try {
            const { MongoClient } = await import('mongodb');
            
            server = new McpServer({
              name: 'MongoDB API',
              version: '1.0.0',
            });
            
            // Connection string from environment
            const connectionString = serverConfig.env.MDB_MCP_CONNECTION_STRING;
            const client = new MongoClient(connectionString);
            
            // Connect to MongoDB
            await client.connect();
            
            // Add a simple query tool
            server.tool(
              'mongodb_query',
              'Execute a MongoDB query',
              {
                database: { type: 'string', description: 'The database name' },
                collection: { type: 'string', description: 'The collection name' },
                query: { type: 'string', description: 'The MongoDB query in JSON format' },
                limit: { type: 'number', description: 'Maximum number of results', optional: true }
              },
              async ({ database, collection, query, limit = 10 }) => {
                try {
                  const db = client.db(database);
                  const coll = db.collection(collection);
                  const parsedQuery = JSON.parse(query);
                  
                  const results = await coll.find(parsedQuery).limit(limit).toArray();
                  
                  return {
                    content: [
                      {
                        type: 'text',
                        text: `Found ${results.length} documents`,
                      },
                      {
                        type: 'json',
                        json: JSON.stringify(results),
                      },
                    ],
                  };
                } catch (error) {
                  return {
                    content: [
                      {
                        type: 'text',
                        text: `Error executing MongoDB query: ${error.message}`,
                      },
                    ],
                  };
                }
              }
            );
          } catch (error) {
            console.error('Failed to initialize MongoDB server:', error);
            server = createFallbackServer(name, error.message);
          }
          break;
          
        default:
          server = new McpServer({
            name: `Generic ${name} Server`,
            version: '1.0.0',
          });
          
          server.tool(
            'info',
            'Get information about this server',
            {},
            async () => {
              return {
                content: [
                  {
                    type: 'text',
                    text: `This is a generic MCP server for "${name}". No specific implementation is available.`,
                  },
                ],
              };
            }
          );
      }
    } catch (error) {
      console.error(`Error creating MCP server "${name}":`, error);
      server = createFallbackServer(name, error.message);
    }
    
    // Handle the request using SSE transport
    const transport = new SseServerTransport({ server });
    return transport.handleRequest(request);
  } catch (error) {
    console.error(`Error handling MCP server "${name}":`, error);
    return new Response('Internal server error', { status: 500 });
  }
}
