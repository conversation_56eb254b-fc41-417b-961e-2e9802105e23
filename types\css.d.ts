// This file provides type definitions for CSS modules and global CSS
declare module '*.css' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

// Type definitions for Tailwind CSS
declare module 'tailwindcss/colors' {
  export const gray: Record<string, string>;
  export const blue: Record<string, string>;
  export const red: Record<string, string>;
  export const green: Record<string, string>;
  export const yellow: Record<string, string>;
  export const purple: Record<string, string>;
  export const pink: Record<string, string>;
  export const indigo: Record<string, string>;
  export const teal: Record<string, string>;
  export const orange: Record<string, string>;
  export const cyan: Record<string, string>;
  export const lime: Record<string, string>;
  export const emerald: Record<string, string>;
  export const fuchsia: Record<string, string>;
  export const violet: Record<string, string>;
  export const sky: Record<string, string>;
  export const rose: Record<string, string>;
  export const amber: Record<string, string>;
  export const lightBlue: Record<string, string>;
  export const warmGray: Record<string, string>;
  export const trueGray: Record<string, string>;
  export const coolGray: Record<string, string>;
  export const blueGray: Record<string, string>;
  export const black: string;
  export const white: string;
  export const current: string;
  export const transparent: string;
  export const inherit: string;
}

// Type definitions for CSS custom properties
declare namespace React {
  interface CSSProperties {
    [key: `--${string}`]: string | number | undefined;
  }
}

// Global styles
declare global {
  // Add global style overrides here if needed
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test';
      // Add other environment variables here
    }
  }
}
