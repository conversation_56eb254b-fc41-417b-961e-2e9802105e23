import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // Only apply in development
  if (process.env.NODE_ENV === 'development') {
    const response = NextResponse.next();
    const origin = request.headers.get('origin');
    if (
      origin &&
      (origin.startsWith('http://localhost:') || origin.startsWith('http://127.0.0.1:'))
    ) {
      response.headers.set('Access-Control-Allow-Origin', origin);
      response.headers.set('Access-Control-Allow-Credentials', 'true');
      response.headers.set('Access-Control-Allow-Headers', '*, Authorization, Content-Type');
      response.headers.set('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
    }
    return response;
  }
  return NextResponse.next();
}

export const config = {
  matcher: '/api/:path*', // Only apply to API routes
};