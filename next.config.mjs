/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable static export for now to handle dynamic routes
  // output: 'export',

  // Disable static optimization for auth pages
  experimental: {
    // Disable server components external packages that might cause issues
    serverComponentsExternalPackages: ['@ai-sdk/provider-utils', '@modelcontextprotocol/sdk'],
    optimizeCss: true,
    optimizePackageImports: ['react-data-grid'],
  },

  // Skip type checking during build for Vercel
  typescript: {
    ignoreBuildErrors: true,
  },

  // Skip ESLint during build for Vercel
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Environment variables that should be available to the client
  env: {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    VERCEL: process.env.VERCEL || '0',
    VERCEL_URL: process.env.VERCEL_URL || 'localhost:3000',
  },

  // Disable MCP server in Vercel environment
  publicRuntimeConfig: {
    disableMCP: process.env.VERCEL === '1',
  },

  output: 'standalone',

  images: {
    unoptimized: true,
    remotePatterns: [
      {
        hostname: 'avatar.vercel.sh',
      },
      {
        hostname: 'lh3.googleusercontent.com',
      },
    ],
  },

  webpack: (config) => {
    // Important: return the modified config
    return config;
  },

  // Disable CSS modules for node_modules
  transpilePackages: ['react-data-grid'],
};

// Remove turbotrace if present
delete nextConfig.experimental?.turbotrace;

export default nextConfig;

