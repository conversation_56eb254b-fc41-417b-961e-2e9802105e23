{"mcpServers": {"supabase-new": {"command": "supabase-mcp-server", "env": {"SUPABASE_PROJECT_REF": "silhiponfpuxjtcghvzg", "SUPABASE_DB_PASSWORD": "IDPAgentic@2025", "SUPABASE_REGION": "eu-central-1"}}, "mongodb": {"command": "node", "args": ["./node_modules/mongodb-mcp-server/dist/index.js"], "env": {"MDB_MCP_CONNECTION_STRING": "mongodb+srv://bishobasha:<EMAIL>/?retryWrites=true&w=majority", "MDB_MCP_API_CLIENT_ID": "mdb_sa_id_681f3ab1decb1a3d3ff04467", "MDB_MCP_API_CLIENT_SECRET": "mdmdb_sa_sk_rHYDdA4F04CNg8TRYqbc78mYe2zjs73i3MvbQCwx"}}}}