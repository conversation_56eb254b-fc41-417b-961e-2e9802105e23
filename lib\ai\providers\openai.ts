import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';
import { isTestEnvironment } from '../../constants';
import { openai as provider } from '@ai-sdk/openai';
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from '../models.test';

// Cheapest OpenAI models (2024):
// - gpt-3.5-turbo (chat, title, artifact)
// - gpt-3.5-turbo (reasoning)

export const openai = isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel,
      },
    })
  : customProvider({
      languageModels: {
        'chat-model': provider('gpt-4.1-mini-2025-04-14'),
        'chat-model-reasoning': wrapLanguageModel({
          model: provider('gpt-4.1-mini'),
          middleware: extractReasoningMiddleware({ tagName: 'think' }),
        }),
        'title-model': provider('gpt-3.5-turbo'),
        'artifact-model': provider('gpt-4.1-mini-2025-04-14'),
      },
    });
