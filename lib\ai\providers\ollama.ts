import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';
import { isTestEnvironment } from '../../constants';
import { ollama as provider } from 'ollama-ai-provider';
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from '../models.test';

// --- LOGGING WRAPPER ---
// List of Ollama models that support tools (function calling)
const OLLAMA_TOOL_MODELS = [
  'llama3:latest',
  'llama3:instruct',
  'llama3:8b-instruct',
  'deepseek-coder:tools',
  'mixtral:tools',
  'qwen:tools',
  'yi:tools',
  'zephyr:tools',
  'openhermes:tools',
  'openchat:tools',
  'command-r:tools',
  'solar:tools',
  'mistral:tools',
  'gemma:tools',
  'dolphin-phi:tools',
  // Add more as needed from https://ollama.com/search?c=tools
];

function modelSupportsTools(modelName: string) {
  return OLLAMA_TOOL_MODELS.some(toolModel => modelName.startsWith(toolModel));
}

function removeToolsDeep(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(removeToolsDeep);
  } else if (obj && typeof obj === 'object') {
    const newObj: any = {};
    for (const key in obj) {
      if (key === 'tools') continue;
      newObj[key] = removeToolsDeep(obj[key]);
    }
    return newObj;
  }
  return obj;
}

function isAsyncIterable(obj: any): obj is AsyncIterable<any> {
  return obj && typeof obj[Symbol.asyncIterator] === 'function';
}

function withOllamaLogging(model: any, modelName: string) {
  const wrapper = {
    async doGenerate(params: any) {
      // === BEGIN OLLAMA LOGGING BLOCK ===
      // Log the incoming message(s) for both title and chat message generation
      if (params && params.prompt) {
        // If prompt is array (messages), log each message
        if (Array.isArray(params.prompt)) {
          params.prompt.forEach((msg: any, idx: number) => {
            console.log(`[Ollama][Message][${modelName}][${idx}]`, msg);
          });
        } else {
          // Single prompt
          console.log(`[Ollama][Message][${modelName}]`, params.prompt);
        }
      } else if (params && params.messages) {
        // Some providers may use 'messages' instead of 'prompt'
        if (Array.isArray(params.messages)) {
          params.messages.forEach((msg: any, idx: number) => {
            console.log(`[Ollama][Message][${modelName}][${idx}]`, msg);
          });
        } else {
          console.log(`[Ollama][Message][${modelName}]`, params.messages);
        }
      }
      console.log('[Ollama][Provider Selected]', {
        provider: 'ollama',
        model: modelName,
        params,
      });
      const ollamaUrl = process.env.OLLAMA_HOST || 'http://localhost:11434';
      try {
        const health = await fetch(`${ollamaUrl}/api/tags`).then(r => r.ok ? 'ok' : 'fail');
        console.log('[Ollama][Status] Health check:', { url: ollamaUrl, status: health });
      } catch (e) {
        console.error('[Ollama][Status] Health check failed:', { url: ollamaUrl, error: e });
      }
      console.log('[Ollama][Request Payload]', {
        url: ollamaUrl,
        model: modelName,
        params,
      });
      try {
        // Remove tools from params if the model does not support tools
        let safeParams = { ...params };
        if (!modelSupportsTools(modelName)) {
          safeParams = removeToolsDeep(safeParams);
        }
        const response = await model.doGenerate(safeParams);
        console.log('[Ollama][Response]', {
          status: 'received',
          model: modelName,
          params: safeParams,
          response,
        });
        return response;
      } catch (error) {
        console.error('[Ollama][Error]', {
          status: 'error',
          model: modelName,
          params,
          error,
        });
        throw error;
      }
      // === END OLLAMA LOGGING BLOCK ===
    },
    async doStream(params: any) {
      console.log('[Ollama][Stream][Start]', { model: modelName, params });
      try {
        // Remove tools from params if the model does not support tools
        let safeParams = { ...params };
        if (!modelSupportsTools(modelName)) {
          safeParams = removeToolsDeep(safeParams);
        }
        const stream = await model.doStream(safeParams);
        if (isAsyncIterable(stream)) {
          for await (const chunk of stream) {
            console.log('[Ollama][Stream][Chunk]', { model: modelName, chunk });
          }
          console.log('[Ollama][Stream][End]', { model: modelName });
          return stream;
        } else {
          console.log('[Ollama][Stream][Non-iterable Result]', { model: modelName, result: stream });
          return stream;
        }
      } catch (error) {
        console.error('[Ollama][Stream][Error]', { model: modelName, error });
        throw error;
      }
    },
  };
  // Attach the real model name for downstream logging/debugging
  (wrapper as any).modelName = modelName;
  return wrapper;
}

export const ollama = isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel,
      },
    })
  : customProvider({
      languageModels: {
        'chat-model': withOllamaLogging(provider('phi3.5:latest'), 'phi3.5:latest'),
        'chat-model-reasoning': wrapLanguageModel({
          model: withOllamaLogging(provider('phi3.5:latest'), 'phi3.5:latest'),
          middleware: extractReasoningMiddleware({ tagName: 'think' }),
        }),
        'title-model': withOllamaLogging(provider('phi3.5:latest'), 'phi3.5:latest'),
        'artifact-model': withOllamaLogging(provider('phi3.5:latest'), 'phi3.5:latest'),
      },
    });