'use client';

import React, { memo, useState, useEffect } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn, formatJSON, formatToolContent } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { LessonMCQ } from '@/components/LessonMCQ';

interface ToolContentProps {
  state: 'call' | 'result';
  toolName: string;
  result?: any;
  args?: any;
  isLoading?: boolean;
}

const variants = {
  collapsed: {
    height: 0,
    opacity: 0,
  },
  expanded: {
    height: 'auto',
    opacity: 1,
  },
};

function PureToolContentCall({
  state,
  toolName,
  result,
  args,
  isLoading,
}: ToolContentProps) {
  const [isExpanded, setIsExpanded] = useState(true);

  useEffect(() => {
    if (result && !isLoading) {
      setTimeout(() => {
        setIsExpanded(false);
      }, 1000);
    }
  }, [result, isLoading]);

  return (
    <Card className="w-full overflow-hidden">
      <CardHeader
        className={'flex flex-row items-center justify-between space-y-0'}
      >
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <span className="font-mono">{toolName}</span>
        </CardTitle>
        <button
          type="button"
          onClick={() => setIsExpanded(!isExpanded)}
          className="rounded-full p-1 hover:bg-muted"
        >
          {isExpanded ? (
            <ChevronUp className="size-4 text-muted-foreground" />
          ) : (
            <ChevronDown className="size-4 text-muted-foreground" />
          )}
        </button>
      </CardHeader>
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            key="content"
            initial="collapsed"
            animate="expanded"
            exit="collapsed"
            variants={variants}
            transition={{ duration: 0.4, ease: 'easeInOut' }}
            style={{ overflow: 'hidden' }}
          >
            <CardContent>
              <div className="space-y-2">
                <div className="rounded-md bg-muted p-3">
                  <div className="text-xs text-muted-foreground mb-1 text-bold">
                    Request
                  </div>
                  <pre className="text-xs font-mono whitespace-pre-wrap break-all">
                    {formatJSON(args)}
                  </pre>
                </div>
                {state === 'result' && (
                  <div className={cn('rounded-md bg-muted p-3')}>
                    <div className="text-xs text-muted-foreground mb-1">
                      Response
                    </div>
                    {/* === MCQ CARD RENDERING BLOCK START === */}
                    {typeof result === 'string' && (result.match(/سؤال|أسئلة|متعددة الخيارات|MCQ/)) ? (() => {
                      // Split by question number (e.g., 1. ... 2. ...)
                      const mcqQuestions = result.split('\n\n').map(mcqBlock => {
                        const [question, ...choices] = mcqBlock.split('\n').filter(Boolean);
                        const validChoices = choices
                          .map(line => {
                            const match = line.match(/^\(?([أ-يA-Za-z])\)?\s*[\)|\.]?\s*(.+)$/);
                            return match ? { id: match[1], text: match[2].trim() } : null;
                          })
                          .filter((c): c is { id: string; text: string } => c !== null);
                        
                        return {
                          question,
                          choices: validChoices,
                        };
                      }).filter(mcq => mcq.choices && mcq.choices.length > 0);
                      if (mcqQuestions.length > 0) {
                        return (
                          <div key={toolName + '-mcq-list'} className="flex flex-col gap-6 my-2">
                            {mcqQuestions.map((mcq, idx) => (
                              <LessonMCQ
                                key={idx}
                                question={mcq.question}
                                choices={mcq.choices.map((c, i) => ({ id: String(i), text: c.text }))}
                              />
                            ))}
                          </div>
                        );
                      }
                      return <pre className="text-xs font-mono whitespace-pre-wrap break-all">{formatToolContent(result)}</pre>;
                    })() : (
                      <pre className="text-xs font-mono whitespace-pre-wrap break-all">{formatToolContent(result)}</pre>
                    )}
                    {/* === MCQ CARD RENDERING BLOCK END === */}
                  </div>
                )}
              </div>
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}

export const ToolContentCall = memo(PureToolContentCall, (prev, next) => {
  if (prev.isLoading !== next.isLoading) return false;
  if (prev.state !== next.state) return false;
  if (prev.toolName !== next.toolName) return false;
  if (prev.result !== next.result) return false;
  if (prev.args !== next.args) return false;

  return true;
});
