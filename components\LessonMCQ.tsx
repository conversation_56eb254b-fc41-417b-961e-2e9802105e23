import React, { useState } from 'react';
import { RadioGroup } from '@headlessui/react';

export type MCQChoice = {
  id: string;
  text: string;
};

export type LessonMCQProps = {
  question: string;
  choices: MCQChoice[];
  correctId?: string;
  onSelect?: (id: string, isCorrect: boolean) => void;
};

import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { HelpCircle, CheckCircle2, XCircle } from 'lucide-react';

export const LessonMCQ: React.FC<LessonMCQProps> = ({ question, choices, correctId, onSelect }) => {
  const [selected, setSelected] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);

  // Log initial render
  console.log('[MCQ] Component rendered with props:', { 
    question, 
    choices: choices?.map(c => ({ id: c.id, text: c.text })), 
    hasCorrectId: !!correctId 
  });

  const handleSelect = (id: string) => {
    console.group('[MCQ] Answer Selection');
    console.log('Question:', question);
    console.log('Selected ID:', id);
    console.log('Correct ID:', correctId);
    console.log('Choices:', choices);
    
    const isCorrect = id === correctId;
    console.log('Is Correct:', isCorrect);
    
    setSelected(id);
    setShowResult(true);
    
    if (onSelect) {
      console.log('Calling onSelect callback');
      onSelect(id, isCorrect);
      
      // Trigger LLM with the result
      console.log('[MCQ] Triggering LLM with result:', { 
        question, 
        selectedAnswer: choices?.find(c => c.id === id)?.text,
        isCorrect,
        correctAnswer: choices?.find(c => c.id === correctId)?.text
      });
      console.groupEnd();
    } else {
      console.warn('[MCQ] onSelect callback is not defined');
    }
  };

  return (
    <Card dir="rtl" className="w-full bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 shadow-lg rounded-xl my-6 overflow-hidden">
      <CardHeader className="border-b border-blue-100 bg-white/50 backdrop-blur-sm">
        <CardTitle className="flex items-center gap-2 text-xl font-bold text-blue-900 text-right">
          <HelpCircle className="h-6 w-6 text-blue-600" />
          سؤال من متعدد
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-6 text-right">
        <div className="font-bold text-lg mb-4 text-blue-900 text-right">{question}</div>
        <RadioGroup value={selected} onChange={handleSelect}>
          <div className="space-y-3">
            {choices.map((choice) => (
              <RadioGroup.Option key={choice.id} value={choice.id}>
                {({ checked }) => (
                  <button
                    className={`w-full px-4 py-2 rounded-lg border transition-all duration-200 focus:outline-none text-right
                      ${checked ? 'bg-blue-500 text-white border-blue-700 shadow-lg scale-105' : 'bg-gray-50 text-blue-900 border-gray-300 hover:bg-blue-100'}
                      ${showResult && correctId === choice.id ? 'ring-2 ring-green-400' : ''}
                      ${showResult && selected === choice.id && correctId !== choice.id ? 'ring-2 ring-red-400' : ''}
                    `}
                    disabled={showResult}
                  >
                    <span className="inline-block rtl:ml-2 ltr:mr-2">
                      <Badge variant="secondary">{choice.id}</Badge>
                    </span>
                    {choice.text}
                    {showResult && correctId === choice.id && (
                      <CheckCircle2 className="inline-block rtl:ml-2 ltr:mr-2 text-green-600" />
                    )}
                    {showResult && selected === choice.id && correctId !== choice.id && (
                      <XCircle className="inline-block rtl:ml-2 ltr:mr-2 text-red-600" />
                    )}
                  </button>
                )}
              </RadioGroup.Option>
            ))}
          </div>
        </RadioGroup>
        {showResult && selected && (
          <div className={`mt-4 font-semibold text-lg text-right ${selected === correctId ? 'text-green-600' : 'text-red-600'}`}>
            {selected === correctId ? 'إجابة صحيحة!' : 'إجابة غير صحيحة، حاول مرة أخرى!'}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
