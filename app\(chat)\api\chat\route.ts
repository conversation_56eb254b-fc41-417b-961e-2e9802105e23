import {
  type UIMessage,
  appendResponseMessages,
  createDataStreamResponse,
  smoothStream,
  streamText,
} from 'ai';
import { auth } from '@/app/(auth)/auth';
import { systemPrompt } from '@/lib/ai/prompts';
import {
  deleteChatById,
  getChatById,
  getMessageById,
  saveChat,
  saveMessages,
  updateMessage,
} from '@/lib/db/queries';
import {
  generateUUID,
  getMostRecentUserMessage,
  getTrailingMessageId,
  processToolCalls,
} from '@/lib/utils';
import { generateTitleFromUserMessage } from '../../actions';
import { isProductionEnvironment } from '@/lib/constants';
import { myProvider } from '@/lib/ai/providers';
import { tools, executableFunctions } from '@/lib/ai/tools';
export const maxDuration = 60;

export async function POST(request: Request) {
  try {
    const {
      id,
      messages,
      selectedChatModel,
    }: {
      id: string;
      messages: Array<UIMessage>;
      selectedChatModel: string;
    } = await request.json();

    // === BEGIN TEMP LOGGING BLOCK ===
    console.log('[API][chat] Incoming request:', {
      id,
      selectedChatModel,
      messages: messages?.length,
    });
    // === END TEMP LOGGING BLOCK ===

    const session = await auth();

    if (!session || !session.user || !session.user.id) {
      console.log('[DEBUG] Early return: Invalid session');
      return new Response('Unauthorized', { status: 401 });
    }

    const userMessage = getMostRecentUserMessage(messages);

    if (!userMessage) {
      console.log('[DEBUG] Early return: No user message found');
      return new Response('No user message found', { status: 400 });
    }

    const chat = await getChatById({ id });

    if (!chat) {
      console.log('[DEBUG] Chat not found, generating title and saving new chat');
      let title;
      try {
        title = await generateTitleFromUserMessage({ message: userMessage });
        console.log('[DEBUG] Title generated:', title);
      } catch (err) {
        console.error('[DEBUG] Error in generateTitleFromUserMessage:', err);
        throw err;
      }
      try {
        await saveChat({ id, userId: session.user.id, title });
        console.log('[DEBUG] Chat saved successfully');
      } catch (err) {
        console.error('[DEBUG] Error in saveChat:', err);
        throw err;
      }
    } else {
      if (chat.userId !== session.user.id) {
        console.log('[DEBUG] Early return: Chat userId does not match session userId');
        return new Response('Unauthorized', { status: 401 });
      }
    }

    // In process tool calls, we will check if the message has been updated
    // and if so, we will update the message in the database.
    let prevUserMessages;
    try {
      prevUserMessages = await getMessageById({ id: userMessage.id });
      console.log('[DEBUG] Got previous user messages:', prevUserMessages.length);
    } catch (err) {
      console.error('[DEBUG] Error in getMessageById:', err);
      throw err;
    }

    if (prevUserMessages.length <= 0) {
      try {
        await saveMessages({
          messages: [
            {
              chatId: id,
              id: userMessage.id,
              role: 'user',
              parts: userMessage.parts,
              attachments: userMessage.experimental_attachments ?? [],
              createdAt: new Date(),
            },
          ],
        });
        console.log('[DEBUG] Saved new user message');
      } catch (err) {
        console.error('[DEBUG] Error in saveMessages:', err);
        throw err;
      }
    }

    console.log('[DEBUG] Passed all early checks, entering createDataStreamResponse');
    return createDataStreamResponse({
      execute: async (dataStream) => {
        let processedMessages, updated;
        try {
          ({ messages: processedMessages, updated } = await processToolCalls(
            {
              messages,
              dataStream,
            },
            executableFunctions,
          ));
          console.log('[DEBUG] processToolCalls succeeded:', { updated, processedMessagesLen: processedMessages.length });
        } catch (err) {
          console.error('[DEBUG] Error in processToolCalls:', err);
          throw err;
        }

        // If the message has been updated, we will update the message in the database.
        // To sync-up and keep tracking the message with the database and dataStream
        if (updated) {
          const updatedMessage = processedMessages.at(-1);
          if (updatedMessage?.id) {
            const prevMessages = await getMessageById({
              id: updatedMessage?.id,
            });

            if (prevMessages) {
              await updateMessage({
                message: {
                  chatId: id,
                  id: updatedMessage.id,
                  role: updatedMessage.role,
                  parts: updatedMessage.parts,
                  attachments: updatedMessage.experimental_attachments ?? [],
                  createdAt: new Date(),
                },
              });
            }
          }
        }

        const executableTools = tools({ session, dataStream });

        // === BEGIN TEMP LOGGING BLOCK ===
        console.log('[API][chat] Using provider/model:', {
          provider: myProvider,
          selectedChatModel,
        });
        // === END TEMP LOGGING BLOCK ===

        // === USER REQUESTED DEBUG LOG ===
        const resolvedModel = myProvider.languageModel(selectedChatModel);
        console.log('[DEBUG] About to call chat model', {
          selectedChatModel,
          resolvedModelType: typeof resolvedModel,
          resolvedModelName: resolvedModel?.modelName || resolvedModel?.name || resolvedModel?.constructor?.name || 'unknown',
          resolvedModel,
          messages: processedMessages
        });
        let result;
        try {
          console.log('[DEBUG] Calling streamText with model:', {
            selectedChatModel,
            resolvedModel,
            messages: processedMessages
          });
          result = streamText({
            model: resolvedModel,
            system: systemPrompt({ selectedChatModel }),
            messages: processedMessages,
            maxSteps: 5,
          experimental_activeTools:
            selectedChatModel === 'chat-model-reasoning'
              ? []
              : Object.keys(executableTools),
          experimental_transform: smoothStream({ chunking: 'word' }),
          experimental_generateMessageId: generateUUID,
          tools: executableTools,
          onFinish: async ({ response }) => {
            if (session.user?.id) {
              try {
                const assistantId = getTrailingMessageId({
                  messages: response.messages.filter(
                    (message) => message.role === 'assistant',
                  ),
                });

                if (!assistantId) {
                  throw new Error('No assistant message found!');
                }

                const [, assistantMessage] = appendResponseMessages({
                  messages: [userMessage],
                  responseMessages: response.messages,
                });

                await saveMessages({
                  messages: [
                    {
                      chatId: id,
                      id: assistantId,
                      role: assistantMessage.role,
                      parts: assistantMessage.parts,
                      attachments:
                        assistantMessage.experimental_attachments ?? [],
                      createdAt: new Date(),
                    },
                  ],
                });
              } catch (error) {
                // === BEGIN TEMP LOGGING BLOCK ===
                console.error('[API][chat] Error:', error);
                // === END TEMP LOGGING BLOCK ===
                console.error(`[error] Failed to save chat`, error);
              }
            }
          },
          experimental_telemetry: {
            isEnabled: isProductionEnvironment,
            functionId: 'stream-text',
          },
        });

        result.consumeStream();

        result.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
        });
      }catch (error) {
        console.error('[API][chat] Uncaught error:', error);
        return new Response('Internal Server Error', { status: 500 });
      }
    }
    });
  } catch (error) {
    return new Response('An error occurred while processing your request!', {
      status: 404,
    });
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Not Found', { status: 404 });
  }

  const session = await auth();

  if (!session || !session.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await getChatById({ id });

    if (chat.userId !== session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    await deleteChatById({ id });

    return new Response('Chat deleted', { status: 200 });
  } catch (error) {
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}