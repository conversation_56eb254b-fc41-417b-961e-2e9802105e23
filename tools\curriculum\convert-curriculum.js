/**
 * Usage:
 * 1. Place your CSV/XLSX files in `tools/curriculum/input`
 * 2. Run `node tools/curriculum/convert-curriculum.js`
 * 3. The converted JSON files will be stored in `public`.
 */


import fs from 'node:fs/promises';
import path from 'node:path';
import { parse as parseCSV } from 'csv-parse/sync';
import xlsx from 'xlsx';

const INPUT_DIR = 'tools/curriculum/input'; // Place your CSV/XLSX files here
const OUTPUT_DIR = 'public';

function normalizeKeys(obj) {
  const newObj = {};
  for (const key in obj) {
    // Remove BOM and trim
    const cleanKey = key.replace(/^\uFEFF/, '').trim();
    newObj[cleanKey] = obj[key];
  }
  return newObj;
}

async function convertCSV(filePath, outPath) {
  const csvContent = await fs.readFile(filePath, 'utf8');
  let rows = parseCSV(csvContent, { columns: true, skip_empty_lines: true });
  rows = rows.map(row => {
    const cleanRow = normalizeKeys(row);
    // Handle subject key regardless of BOM
    if (cleanRow['المادة']) {
      cleanRow['المادة'] = `${cleanRow['المادة']}: arabic`;
    }
    return cleanRow;
  });
  await fs.writeFile(outPath, JSON.stringify(rows, null, 2), 'utf8');
}

async function convertXLSX(filePath, outPath) {
  const workbook = xlsx.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  let rows = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);
  rows = rows.map(row => ({
    ...row,
    'المادة': row['المادة'] ? `${row['المادة']}: arabic` : undefined,
  }));
  await fs.writeFile(outPath, JSON.stringify(rows, null, 2), 'utf8');
}

async function main() {
  await fs.mkdir(OUTPUT_DIR, { recursive: true });
  const files = await fs.readdir(INPUT_DIR);
  for (const file of files) {
    const ext = path.extname(file).toLowerCase();
    const inPath = path.join(INPUT_DIR, file);
    const outPath = path.join(OUTPUT_DIR, file.replace(/\.(csv|xlsx)$/i, '.json'));
    if (ext === '.csv') {
      await convertCSV(inPath, outPath);
      console.log(`Converted ${file} to JSON.`);
    } else if (ext === '.xlsx') {
      await convertXLSX(inPath, outPath);
      console.log(`Converted ${file} to JSON.`);
    }
  }
}

main().catch(console.error);
