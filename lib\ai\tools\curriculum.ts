import { tool } from 'ai';
import { z } from 'zod';
import path from 'node:path';

// Path to curriculum JSON - handle both development and production
async function getCurriculumPaths() {
  const fs = await import('node:fs/promises');

  // Try multiple possible paths
  const possiblePaths = [
    path.join(process.cwd(), 'public', 'curriculum.json'),
    path.join(process.cwd(), 'curriculum.json'),
    './public/curriculum.json',
    './curriculum.json',
    'public/curriculum.json',
    'curriculum.json'
  ];

  console.log(`[Curriculum] Current working directory: ${process.cwd()}`);
  console.log(`[Curriculum] NODE_ENV: ${process.env.NODE_ENV}`);

  for (const filePath of possiblePaths) {
    try {
      await fs.access(filePath);
      console.log(`[Curriculum] Found curriculum file at: ${filePath}`);
      return filePath;
    } catch (error) {
      console.log(`[Curriculum] File not found at: ${filePath}`);
    }
  }

  throw new Error('Curriculum JSON file not found in any expected location');
}

async function readCurriculumJSON() {
  const fs = await import('node:fs/promises');
  try {
    const curriculumPath = await getCurriculumPaths();
    console.log(`[Curriculum] Reading from: ${curriculumPath}`);
    const jsonContent = await fs.readFile(curriculumPath, 'utf8');
    return JSON.parse(jsonContent);
  } catch (error) {
    console.error(`[Curriculum] Error reading curriculum JSON:`, error);
    throw new Error(`Error reading curriculum JSON: ${error.message}`);
  }
}

export const curriculumList = tool({
  description: 'List all curriculum entries from the curriculum JSON file',
  parameters: z.object({}),
  execute: async () => {
    const entries = await readCurriculumJSON();
    return {
      count: entries.length,
      entries,
    };
  },
});

export const curriculumSearch = tool({
  description: 'Search curriculum entries by keyword (any field)',
  parameters: z.object({
    query: z.string(),
  }),
  execute: async ({ query }) => {
    const entries = await readCurriculumJSON();
    const results = entries.filter((entry: Record<string, string>) =>
      Object.values(entry)
        .join(' ')
        .toLowerCase()
        .includes(query.toLowerCase())
    );
    return {
      query,
      count: results.length,
      results,
    };
  },
});

// New: List all lessons/classes with summary info and unique lessonId
export const curriculumLessonList = tool({
  description: 'List all lessons with الرقم as the unique lessonId. (قائمة الدروس)',
  parameters: z.object({}),
  execute: async () => {
    const entries = await readCurriculumJSON();
    const lessons = entries.map((entry: Record<string, string>) => ({
      subject: entry['المادة'],
      grade: entry['الصف'],
      unit: entry['الوحدة'],
      lessonNumber: entry['الرقم'],
      lessonId: entry['الرقم'], // Use الرقم as id
      lessonName: entry['الوحدة'], // Or any other field you want to show
    }));
    return lessons;
  },
});

// New: Fetch lesson content by lessonId
export const curriculumLessonContent = tool({
  description: 'Get full lesson content by الرقم (unique lesson id). (محتوى الدرس الكامل)',
  parameters: z.object({
    lessonId: z.string(),
  }),
  execute: async ({ lessonId }) => {
    const entries = await readCurriculumJSON();
    const match = entries.find((entry: Record<string, string>) => entry['الرقم'] === lessonId);
    if (!match) {
      return { error: 'Lesson not found' };
    }
    return {
      subject: match['المادة'],
      grade: match['الصف'],
      unit: match['الوحدة'],
      lessonNumber: match['الرقم'],
      lessonName: match['الوحدة'],
      content: match['كتاب الطالب'] || '',
      teacherGuide: match['دليل المعلم'] || '',
      activityBook: match['كتاب النشاط'] || '',
      notes: match['الملاحظات'] || '',
    };
  },
});

// Get lesson content and metadata for summary generation
export const curriculumLessonSummary = tool({
  description: 'Get lesson content and metadata for summary generation by lessonId. (تلخيص الدرس)',
  parameters: z.object({ lessonId: z.string() }),
  execute: async ({ lessonId }) => {
    const lesson = await curriculumLessonContent.execute({ lessonId });
    if ((lesson as any).error) return lesson;
    return {
      subject: lesson.subject,
      grade: lesson.grade,
      unit: lesson.unit,
      lessonNumber: lesson.lessonNumber,
      lessonName: lesson.lessonName,
      content: lesson.content,
      type: 'summary',
    };
  },
});

// Get lesson content and metadata for question generation
export const curriculumLessonQuestions = tool({
  description: 'Get lesson content and metadata for question generation by lessonId. (أسئلة على الدرس)',
  parameters: z.object({ lessonId: z.string() }),
  execute: async ({ lessonId }) => {
    const lesson = await curriculumLessonContent.execute({ lessonId });
    if ((lesson as any).error) return lesson;
    return {
      subject: lesson.subject,
      grade: lesson.grade,
      unit: lesson.unit,
      lessonNumber: lesson.lessonNumber,
      lessonName: lesson.lessonName,
      content: lesson.content,
      type: 'questions',
    };
  },
});

// Get lesson content and metadata for lesson plan generation
export const curriculumLessonPlan = tool({
  description: 'Get lesson content and metadata for lesson plan generation by lessonId. (خطة تحضير الدرس)',
  parameters: z.object({ lessonId: z.string() }),
  execute: async ({ lessonId }) => {
    const lesson = await curriculumLessonContent.execute({ lessonId });
    if ((lesson as any).error) return lesson;
    return {
      subject: lesson.subject,
      grade: lesson.grade,
      unit: lesson.unit,
      lessonNumber: lesson.lessonNumber,
      lessonName: lesson.lessonName,
      content: lesson.content,
      type: 'plan',
    };
  },
});
