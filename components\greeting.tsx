import { motion } from 'framer-motion';

// Use Cairo font for Arabic
import { Cairo } from 'next/font/google';
const cairo = Cairo({ subsets: ['arabic'], weight: ['400', '700'], variable: '--font-cairo' });

export const Greeting = () => {
  return (
    <div
      key="overview"
      className="max-w-3xl mx-auto md:mt-20 px-8 size-full flex flex-col justify-center"
    >
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 0 }}
        transition={{ delay: 0.5 }}
        className="text-2xl font-semibold font-cairo"
        style={{ fontFamily: 'var(--font-cairo)' }}
      >
        مرحبًا بك مع الهدهد الذكي
        </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ delay: 0.6 }}
        className="text-2xl text-zinc-500"
      >
        كيف يمكنني مساعدتك اليوم؟
      </motion.div>
    </div>
  );
};
