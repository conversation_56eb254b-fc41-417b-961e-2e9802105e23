{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["next-env.d.ts", "src/types/*.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next.config.js", "types/**/*.d.ts"], "exclude": ["node_modules", "**/node_modules/*"]}