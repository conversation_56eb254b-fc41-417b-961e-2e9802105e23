import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';
import { MongoClient } from 'mongodb';

export function createServer(env: Record<string, string>) {
  const server = new McpServer({
    name: 'MongoDB API',
    version: '1.0.0',
  });

  // Connection string from environment
  const connectionString = env.MDB_MCP_CONNECTION_STRING;
  const client = new MongoClient(connectionString);
  
  // Connect to MongoDB
  let connected = false;
  client.connect().then(() => {
    connected = true;
    console.log('Connected to MongoDB');
  }).catch(err => {
    console.error('Failed to connect to MongoDB:', err);
  });

  // Add MongoDB tools
  server.tool(
    'mongodb_query',
    'Execute a MongoDB query',
    {
      database: z.string().describe('The database name'),
      collection: z.string().describe('The collection name'),
      query: z.string().describe('The MongoDB query in JSON format'),
      limit: z.number().optional().describe('Maximum number of results to return')
    },
    async ({ database, collection, query, limit = 10 }) => {
      if (!connected) {
        return {
          content: [
            {
              type: 'text',
              text: 'Error: Not connected to MongoDB',
            },
          ],
        };
      }

      try {
        const db = client.db(database);
        const coll = db.collection(collection);
        const parsedQuery = JSON.parse(query);
        
        const results = await coll.find(parsedQuery).limit(limit).toArray();
        
        return {
          content: [
            {
              type: 'text',
              text: `Found ${results.length} documents`,
            },
            {
              type: 'json',
              json: JSON.stringify(results),
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error executing MongoDB query: ${error.message}`,
            },
          ],
        };
      }
    }
  );

  return server;
}