import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';

export function createServer(env: Record<string, string>) {
  const server = new McpServer({
    name: 'Supabase API',
    version: '1.0.0',
  });

  // Supabase configuration from environment
  const projectRef = env.SUPABASE_PROJECT_REF;
  const region = env.SUPABASE_REGION || 'eu-central-1';
  const dbPassword = env.SUPABASE_DB_PASSWORD;
  
  const supabaseUrl = `https://${projectRef}.supabase.co`;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';
  
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Add Supabase tools
  server.tool(
    'supabase_query',
    'Execute a Supabase query',
    {
      table: z.string().describe('The table name'),
      select: z.string().describe('Columns to select'),
      filter: z.string().optional().describe('Filter condition'),
      limit: z.number().optional().describe('Maximum number of results to return')
    },
    async ({ table, select, filter, limit = 10 }) => {
      try {
        let query = supabase.from(table).select(select).limit(limit);
        
        if (filter) {
          // Parse filter as JSON { column: value } or { column: { operator: value } }
          const filterObj = JSON.parse(filter);
          Object.entries(filterObj).forEach(([column, condition]) => {
            if (typeof condition === 'object') {
              Object.entries(condition as Record<string, any>).forEach(([op, value]) => {
                switch (op) {
                  case 'eq': query = query.eq(column, value); break;
                  case 'neq': query = query.neq(column, value); break;
                  case 'gt': query = query.gt(column, value); break;
                  case 'lt': query = query.lt(column, value); break;
                  case 'gte': query = query.gte(column, value); break;
                  case 'lte': query = query.lte(column, value); break;
                  case 'like': query = query.like(column, value); break;
                  case 'ilike': query = query.ilike(column, value); break;
                  case 'in': query = query.in(column, value); break;
                  // Add other operators as needed
                }
              });
            } else {
              query = query.eq(column, condition);
            }
          });
        }
        
        const { data, error } = await query;
        
        if (error) throw error;
        
        return {
          content: [
            {
              type: 'text',
              text: `Found ${data.length} records`,
            },
            {
              type: 'json',
              json: JSON.stringify(data),
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error executing Supabase query: ${error.message}`,
            },
          ],
        };
      }
    }
  );

  return server;
}