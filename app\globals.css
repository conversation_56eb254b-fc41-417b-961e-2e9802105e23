/* Import Tailwind CSS */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Apply font to all elements */
* {
  font-family: var(--font-cairo), 'Cairo', sans-serif;
}

/* Ensure body uses the correct font */
body {
  font-family: var(--font-cairo), 'Cairo', sans-serif;
}

/* Base styles */
:root {
  /* Font family */
  --font-sans: var(--font-cairo), 'Cairo', sans-serif;
  /* RGB colors */
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
  
  /* Theme colors */
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 240 5.9% 10%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 240 5.9% 10%;
  --radius: 0.5rem;
  
  /* Chart colors */
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  
  /* Sidebar colors */
  --sidebar-background: 0 0% 98%;
  --sidebar-foreground: 240 5.3% 26.1%;
  --sidebar-primary: 240 5.9% 10%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 5.9% 10%;
  --sidebar-border: 220 13% 91%;
  --sidebar-ring: 217.2 91.2% 59.8%;
}

/* Dark mode colors */
.dark {
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 0, 0, 0;
  --background-end-rgb: 0, 0, 0;
  
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 240 5.9% 10%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
  
  /* Dark mode sidebar colors */
  --sidebar-background: 240 10% 3.9%;
  --sidebar-foreground: 0 0% 98%;
  --sidebar-primary: 0 0% 98%;
  --sidebar-primary-foreground: 240 5.9% 10%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 0 0% 98%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 240 4.9% 83.9%;
}

/* Base styles */
@layer base {
  * {
    border-color: hsl(var(--border));
    box-sizing: border-box;
    padding: 0;
    margin: 0;
  }
  
  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  body {
    min-height: 100vh;
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: 'rlig' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background-color: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted-foreground) / 0.3);
    border-radius: 9999px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground) / 0.5);
  }
}

/* Utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* RTL Support */
  .rtl {
    direction: rtl;
  }
  
  .rtl-list {
    direction: rtl;
    text-align: right;
    list-style-position: inside;
  }
  
  /* Animation */
  .animate-in {
    animation: animateIn 0.3s ease-out forwards;
  }
  
  @keyframes animateIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Additional base styles */
@layer base {
  * {
    border-color: hsl(var(--border));
  }
  
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

.skeleton * {
  pointer-events: none !important;
}

.skeleton [class^='text-'] {
  color: transparent;
  border-radius: 0.375rem;
  background-color: hsl(var(--foreground) / 0.2);
  user-select: none;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton .skeleton-bg {
  background-color: hsl(var(--foreground) / 0.1);
}

.skeleton .skeleton-div {
  background-color: hsl(var(--foreground) / 0.2);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.ProseMirror {
  outline: none;
}

.cm-editor,
.cm-gutters {
  background-color: var(--background);
  outline: none;
}

.dark .cm-editor,
.dark .cm-gutters {
  background-color: hsl(240 3.7% 15.9%);
}

.cm-editor *::selection,
.cm-gutters *::selection {
  background-color: hsl(var(--foreground) / 0.1) !important;
}

.ͼo.cm-focused>.cm-scroller>.cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
  background-color: hsl(240 5.9% 90%) !important;
}

.dark .ͼo.cm-focused>.cm-scroller>.cm-selectionLayer .cm-selectionBackground,
.dark .ͼo.cm-selectionBackground,
.dark .ͼo.cm-content::selection {
  background-color: hsl(240 3.7% 15.9%) !important;
}

.cm-activeLine,
.cm-activeLineGutter {
  background-color: transparent !important;
}

.cm-activeLine {
  border-top-right-radius: 0.125rem !important;
  border-bottom-right-radius: 0.125rem !important;
}

.cm-lineNumbers {
  min-width: 1.75rem;
}

.cm-foldGutter {
  min-width: 0.75rem;
}

.cm-lineNumbers .cm-activeLineGutter {
  border-top-left-radius: 0.125rem !important;
  border-bottom-left-radius: 0.125rem !important;
}

.suggestion-highlight {
  background-color: hsl(221.2 83.2% 53.3% / 0.1);
  color: hsl(221.2 83.2% 53.3%);
  transition: background-color 0.2s;
}

.suggestion-highlight:hover {
  background-color: hsl(217.2 91.2% 59.8% / 0.2);
}

.dark .suggestion-highlight {
  background-color: hsl(217.2 91.2% 59.8% / 0.2);
  color: hsl(210 40% 98%);
}

.dark .suggestion-highlight:hover {
  background-color: hsl(217.2 91.2% 59.8% / 0.3);
}

::-webkit-scrollbar {
  width: 17px;
  height: 17px;
}

::-webkit-scrollbar-track {
  background-color: var(--scroll-bar-track-bg-color);
  opacity: 0.01;
}

::-webkit-scrollbar-thumb {
  background: var(--scroll-bar-handle-bg-color);
  box-shadow: inset 0 0 6px var(--scroll-bar-handle-bg-color);
  -webkit-box-shadow: inset 0 0 6px var(--scroll-bar-handle-bg-color);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: var(--scroll-bar-track-bg-color);
}