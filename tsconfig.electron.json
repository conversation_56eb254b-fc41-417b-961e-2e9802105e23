{"compilerOptions": {"composite": true, "incremental": true, "tsBuildInfoFile": ".tscache/cache-electron", "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "lib": ["es2021", "dom", "ESNext.Promise"], "jsx": "react", "moduleResolution": "bundler", "target": "es2022", "module": "es2022", "outDir": "build", "declaration": true, "declarationDir": "build", "rootDir": "electron", "resolveJsonModule": true, "baseUrl": ".", "typeRoots": ["./node_modules/@types", "./src/types"], "paths": {"*": ["./node_modules/*", "./src/types/*"]}}, "include": ["electron/**/*.ts", "electron/**/*.json", "src/types/*.d.ts"], "exclude": ["node_modules"]}