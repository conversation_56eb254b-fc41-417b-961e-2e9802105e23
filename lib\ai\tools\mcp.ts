import fs from 'node:fs/promises';
import { Experimental_StdioMCPTransport } from 'ai/mcp-stdio';
import {
  experimental_createMCPClient,
  type Tool,
  type MCPTransport as MCPTransportType,
} from 'ai';
import { fileURLToPath } from 'node:url';
import path from 'node:path';

const MCP_SERVER_CONFIG_PATH =
  process.env.MCP_SERVER_CONFIG_PATH ?? './mcp-servers.json';
const MCP_SERVER_DEFAULT_CONFIG_NAME =
  process.env.MCP_SERVER_DEFAULT_CONFIG_NAME ?? 'mcpServers';
const IS_VERCEL = process.env.VERCEL === '1';

type Experimental_SseMCPTransport = Exclude<
  Parameters<typeof experimental_createMCPClient>[0]['transport'],
  MCPTransportType
>;

type MCPTransport =
  | Experimental_StdioMCPTransport
  | Experimental_SseMCPTransport;

async function readMCPServerConfig(
  mcpServerConfigPath: string,
  defaultConfigName?: string,
) {
  const resolvedPath = path.resolve(path.dirname(fileURLToPath(import.meta.url)), '..', mcpServerConfigPath);
  const json = JSON.parse(await fs.readFile(mcpServerConfigPath, 'utf-8'));
  const config = defaultConfigName ? json[defaultConfigName] : json;

  if (!config) {
    throw new Error(
      `Not found ${defaultConfigName} property in MCP server config`,
    );
  }

  return config;
}

export type MCPTransportConfig = Record<string, MCPTransport>;
export type MCPTool = Tool;

async function createMCPTransports(
  config: Record<string, any>,
): Promise<MCPTransportConfig> {
  try {
    return Object.entries(config).reduce((acc, [name, option]) => {
      if (IS_VERCEL) {
        // For Vercel, always use SSE transport with API routes
        const baseUrl = process.env.VERCEL_URL 
          ? `https://${process.env.VERCEL_URL}` 
          : 'http://localhost:3000';
        
        acc[name] = { 
          type: 'sse', 
          url: `${baseUrl}/api/mcp/${name}` 
        } as Experimental_SseMCPTransport;
      } else if (option.command) {
        // For local development, use stdio transport
        acc[name] = new Experimental_StdioMCPTransport({
          command: option.command,
          args: option.args,
          env: option.env,
          stderr: option.stderr,
          cwd: option.cwd,
        });
      } else if (option.url) {
        // For custom SSE endpoints
        acc[name] = { type: 'sse', ...option } as Experimental_SseMCPTransport;
      } else {
        throw new Error('MCP server config has no command or url property.');
      }

      return acc;
    }, {} as MCPTransportConfig);
  } catch (error) {
    console.error('Failed parsed MCP configs', error);
    throw error;
  }
}

async function createMCPTools() {
  try {
    const config = await readMCPServerConfig(
      MCP_SERVER_CONFIG_PATH,
      MCP_SERVER_DEFAULT_CONFIG_NAME,
    );

    const transports = await createMCPTransports(config);

    // Create clients with error handling
    const clients = await Promise.all(
      Object.keys(transports).map(async (name) => {
        try {
          return await experimental_createMCPClient({ transport: transports[name] });
        } catch (error) {
          console.error(`Failed to create MCP client for "${name}":`, error);
          return null;
        }
      })
    ).then(results => results.filter(Boolean)); // Filter out null clients

    const tools: Record<string, MCPTool> = {};

    for (const client of clients) {
      if (!client) continue;
      
      try {
        const clientTools = await client.tools();
        for (const [name, tool] of Object.entries(clientTools)) {
          tools[name] = tool;
        }
      } catch (error) {
        console.error(`Failed to get tools from client:`, error);
      }
    }

    const toolList = Object.keys(tools);

    return {
      clients,
      tools,
      toolList,
    };
  } catch (error) {
    console.error('Failed to create MCP tools:', error);
    
    // Return empty tools object in case of error
    return {
      clients: [],
      tools: {},
      toolList: [],
    };
  }
}

const mcp = await createMCPTools();

Object.freeze(mcp);

export { mcp };
