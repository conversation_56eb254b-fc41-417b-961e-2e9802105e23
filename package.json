{"name": "electron-ai-chatbot", "version": "0.1.0", "private": true, "type": "module", "main": "build/index.js", "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "e:dev": "tsc-watch --noClear --onFirstSuc<PERSON> \"electron .\" --project tsconfig.electron.json", "e:build": "tsc --project tsconfig.electron.json && electron-builder --config electron-builder.yml", "e:start": "electron .", "lint": "next lint && biome lint --write --unsafe", "lint:fix": "next lint --fix && biome lint --write --unsafe", "format": "biome format --write", "db:generate": "drizzle-kit generate", "db:migrate": "npx tsx lib/db/migrate.ts", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "db:pull": "drizzle-kit pull", "db:check": "drizzle-kit check", "db:up": "drizzle-kit up", "test": "export PLAYWRIGHT=True && pnpm exec playwright test --workers=4"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.9", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/fal": "^0.1.11", "@ai-sdk/fireworks": "^0.2.14", "@ai-sdk/google": "^1.2.17", "@ai-sdk/groq": "^1.2.9", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.8", "@ai-sdk/togetherai": "^0.2.14", "@ai-sdk/xai": "^1.2.10", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-python": "^6.1.6", "@codemirror/state": "^6.5.0", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.35.3", "@esbuild-kit/core-utils": "^3.3.2", "@esbuild-kit/esm-loader": "^2.6.5", "@headlessui/react": "^2.2.2", "@openrouter/ai-sdk-provider": "^0.4.5", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@radix-ui/react-visually-hidden": "^1.1.0", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^0.24.1", "@vercel/postgres": "^0.10.0", "a-calc": "^2.2.14", "ai": "^4.3.16", "bcrypt-ts": "^5.0.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^6.0.1", "critters": "^0.0.23", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "diff-match-patch": "^1.0.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.43.1", "electron-default-menu": "^1.0.2", "fast-deep-equal": "^3.1.3", "fast-fuzzy": "^1.12.0", "formik": "^2.4.6", "framer-motion": "^11.3.19", "geist": "^1.3.1", "lucide-react": "^0.446.0", "mongodb-mcp-server": "^0.1.0", "nanoid": "^5.0.8", "next": "^14.1.0", "next-auth": "5.0.0-beta.25", "next-electron-rsc": "^0.2.3", "next-themes": "^0.4.6", "ollama-ai-provider": "^1.2.0", "orderedmap": "^2.1.1", "papaparse": "^5.5.2", "postgres": "^3.4.4", "prosemirror-example-setup": "^1.2.3", "prosemirror-inputrules": "^1.4.0", "prosemirror-markdown": "^1.13.1", "prosemirror-model": "^1.23.0", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.34.3", "react": "^18.2.0", "react-data-grid": "7.0.0-beta.47", "react-dom": "^18.2.0", "react-hook-form": "^7.54.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.1.7", "remark-gfm": "^4.0.0", "server-only": "^0.0.1", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.6.0", "tailwindcss-rtl": "^0.9.0", "usehooks-ts": "^3.1.0", "xlsx": "^0.18.5", "yup": "^1.6.1", "zod": "^3.23.8", "@supabase/supabase-js": "^2.39.7", "mongodb": "^6.3.0", "@modelcontextprotocol/sdk": "^1.9.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@playwright/test": "^1.50.1", "@tailwindcss/typography": "^0.5.15", "@types/d3-scale": "^4.0.8", "@types/node": "^22.8.6", "@types/papaparse": "^5.3.15", "@types/pdf-parse": "^1.1.4", "@types/pg": "^8.10.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.1", "electron": "^35.1.5", "electron-builder": "^26.0.12", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-tailwindcss": "^3.17.5", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "tsc-watch": "^6.2.1", "tsx": "^4.19.1", "typescript": "^5.6.3"}, "packageManager": "npm@10.9.2", "engines": {"node": ">=22.0.0"}}