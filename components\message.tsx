'use client';

import React from 'react';

import type { UIMessage } from 'ai';
import { LessonCard } from './LessonCard';
import { LessonSummary } from './LessonSummary';
import { LessonMCQ } from './LessonMCQ';
import cx from 'classnames';
import { AnimatePresence, motion } from 'framer-motion';
import { memo, startTransition, useState } from 'react';
import type { Vote } from '@/lib/db/schema';
import { DocumentToolCall, DocumentToolResult } from './document';
import { PencilEditIcon, SparklesIcon } from './icons';
import { Markdown } from './markdown';
import { MessageActions } from './message-actions';
import { PreviewAttachment } from './preview-attachment';
import { Weather } from './weather';
import equal from 'fast-deep-equal';
import { APPROVAL, cn } from '@/lib/utils';
import { Button } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { MessageEditor } from './message-editor';
import { DocumentPreview } from './document-preview';
import { MessageReasoning } from './message-reasoning';
import type { useChat, UseChatHelpers } from '@ai-sdk/react';
import { ToolContentCall } from './tool-content';
import { ToolPermissionRequest } from './tool-permission-request';
import type { ToolMetadata } from '../lib/ai/tools';
import { Calculator } from './calculator';
import { PokemonCarousel } from './pokemon-carousel';
import useSWR from 'swr';

const PurePreviewMessage = ({
  chatId,
  message,
  vote,
  isLoading,
  setMessages,
  addToolResult,
  reload,
  isReadonly,
  tools,
  setInput,
}: {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: UseChatHelpers['setMessages'];
  addToolResult?: ReturnType<typeof useChat>['addToolResult'];
  reload: UseChatHelpers['reload'];
  isReadonly: boolean;
  tools?: Record<string, ToolMetadata>;
  setInput?: UseChatHelpers['setInput'];
}) => {
  const [mode, setMode] = useState<'view' | 'edit'>('view');

  const { data, mutate } = useSWR<Record<string, any>>(`/chat/${chatId}`);

  // Define types for MCQ data structure
  interface MCQChoice {
    id: string;
    text: string;
  }

  interface MCQData {
    type: 'mcq';
    questions: Array<{
      id: string;
      question: string;
      choices: MCQChoice[];
      correctId: string;
    }>;
    suggestedActions?: Array<{
      action: string;
      label: string;
      icon?: string;
      prompt?: string;
    }>;
  }

  const handleMCQSelect = async (selectedId: string, isCorrect: boolean) => {
    try {
      console.group('[Message] Handling MCQ Selection');
      console.log('Selected ID:', selectedId);
      console.log('Is Correct:', isCorrect);
      
      // Find the MCQ data from the message parts
      const mcqData = message.parts?.find(
        (p): p is { type: 'text'; text: string } => 
          p.type === 'text' && p.text.trim().startsWith('{')
      );

      console.log('Found MCQ data:', mcqData ? 'Yes' : 'No');

      let questionText = 'سؤال';
      let selectedChoiceText = 'إجابة مختارة';
      let correctAnswerText = 'إجابة صحيحة';
      let choices: Array<{id: string, text: string}> = [];

      // Try to parse the MCQ data if available
      try {
        if (mcqData) {
          console.log('Raw MCQ data:', mcqData.text.trim());
          const parsedData: MCQData = JSON.parse(mcqData.text.trim());
          console.log('Parsed MCQ data:', parsedData);
          
          // Handle both single question and multiple questions format
          const questions = parsedData.questions || [];
          if (questions.length > 0) {
            const firstQuestion = questions[0];
            questionText = firstQuestion.question || questionText;
            
            // Find the selected choice text
            const selectedChoice = firstQuestion.choices?.find((c: {id: string}) => c.id === selectedId);
            selectedChoiceText = selectedChoice?.text || selectedChoiceText;
            
            // Find the correct answer text
            const correctChoice = firstQuestion.choices?.find(
              (c: {id: string}) => c.id === firstQuestion.correctId
            );
            correctAnswerText = correctChoice?.text || correctAnswerText;
            choices = firstQuestion.choices || [];
          }
          
          console.log('Extracted MCQ info:', {
            questionText,
            selectedChoiceText,
            correctAnswerText,
            choices: choices.map((c: {id: string, text: string}) => ({id: c.id, text: c.text}))
          });
        }
      } catch (e) {
        console.error('Error parsing MCQ data:', e);
      }

      // Create a user message with the MCQ response
      const responseText = `[MCQ_ANSWER] ${isCorrect ? 'CORRECT' : 'INCORRECT'}\n` +
        `السؤال: ${questionText}\n` +
        `إجابتك: ${selectedChoiceText}\n` +
        `الجواب الصحيح: ${correctAnswerText}\n` +
        `النتيجة: ${isCorrect ? '✅ إجابة صحيحة' : '❌ إجابة خاطئة'}`;
      
      console.log('Creating user message with response:', responseText);
      
      const userMessage: UIMessage = {
        id: `msg-${Date.now()}`,
        role: 'user',
        content: responseText,
        parts: [{ 
          type: 'text', 
          text: responseText,
          display: { role: 'user' }
        }],
        createdAt: new Date(),
        display: { role: 'user' },
        metadata: { 
          isMCQResponse: true, 
          isCorrect, 
          selectedId,
          question: questionText,
          selectedChoice: selectedChoiceText
        }
      } as unknown as UIMessage;
      
      console.log('[Message] Adding MCQ response to messages:', userMessage);
      
      // Add the user's response to the message history
      setMessages(prev => [...prev, userMessage]);
      
      // If the answer is incorrect, automatically ask for an explanation
      if (!isCorrect) {
        const explanationRequest: UIMessage = {
          id: `msg-${Date.now() + 1}`,
          role: 'user',
          content: 'الرجاء شرح الإجابة الصحيحة',
          parts: [{
            type: 'text',
            text: 'الرجاء شرح الإجابة الصحيحة',
            display: { role: 'user' }
          }],
          createdAt: new Date(),
          display: { role: 'user' }
        } as unknown as UIMessage;
        
        // Add the explanation request after a short delay
        setTimeout(() => {
          setMessages(prev => [...prev, explanationRequest]);
        }, 500);
      }
    } catch (error) {
      console.error('Error handling MCQ selection:', error);
      // Optionally show an error message to the user
      const errorMessage: UIMessage = {
        id: `error-${Date.now()}`,
        role: 'assistant',
        content: 'حدث خطأ أثناء معالجة إجابتك. يرجى المحاولة مرة أخرى.',
        parts: [{
          type: 'text',
          text: 'حدث خطأ أثناء معالجة إجابتك. يرجى المحاولة مرة أخرى.',
          display: { role: 'assistant' }
        }],
        createdAt: new Date(),
        display: { role: 'assistant' }
      } as unknown as UIMessage;
      
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const handleApproveResult = ({
    toolName,
    toolCallId,
    result,
    always,
  }: {
    toolName: string;
    toolCallId: string;
    result: any;
    always?: boolean;
  }) => {
    addToolResult?.({
      toolCallId,
      result,
    });

    if (always) {
      mutate({
        ...data,
        approved: [...(data?.approved ?? []), toolName],
      });
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn(
            'flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl',
            {
              'w-full': mode === 'edit',
              'group-data-[role=user]/message:w-fit': mode !== 'edit',
            },
          )}
        >
          {message.role === 'assistant' && (
            <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background">
              <div>
                <SparklesIcon size={14} />
              </div>
            </div>
          )}

          <div className="flex flex-col gap-4 w-full">
            {message.experimental_attachments && (
              <div
                data-testid={`message-attachments`}
                className="flex flex-row justify-end gap-2"
              >
                {message.experimental_attachments.map((attachment) => (
                  <PreviewAttachment
                    key={attachment.url}
                    attachment={attachment}
                  />
                ))}
              </div>
            )}

            {message.parts?.map((part, index) => {
              const { type } = part;
              const key = `message-${message.id}-part-${index}`;

              if (type === 'reasoning') {
                return (
                  <MessageReasoning
                    key={key}
                    isLoading={isLoading}
                    reasoning={part.reasoning}
                  />
                );
              }

              if (type === 'text') {
                if (mode === 'view') {
                  // === LLM LESSON LIST TO CARD VIEW BLOCK START ===
                  // Try to match a numbered Arabic lesson list and render as cards if possible
                  const lessonListMatches = part.text.match(/^(\d+)\.?\s+(.+)$/gm);
                  // Find the latest curriculumLessonList tool result in this message
                  let toolLessonList: any[] | undefined = undefined;
                  if (message.parts) {
                    for (const p of message.parts) {
                      if (p.type === 'tool-invocation' && p.toolInvocation?.toolName === 'curriculumLessonList' && p.toolInvocation?.state === 'result') {
                        toolLessonList = Array.isArray(p.toolInvocation.result) ? p.toolInvocation.result : undefined;
                      }
                    }
                  }
                  // === LLM LESSON LIST JSON CARD VIEW BLOCK START ===
                  try {
                    const parsed = JSON.parse(part.text.trim());
                    if (parsed && parsed.type === "lesson_list" && Array.isArray(parsed.lessons)) {
                      return (
                        <div key={key} className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 my-6">
                          {parsed.lessons.map((lesson: any, idx) => (
                            <LessonCard
                              key={`lesson-${lesson.lessonId ?? ''}-${idx}`}
                              {...lesson}
                              className="border border-blue-200 shadow-sm bg-blue-50"
                            />
                          ))}
                        </div>
                      );
                    }
                  } catch {}
                  // === LLM LESSON LIST JSON CARD VIEW BLOCK END ===

                  if (lessonListMatches && toolLessonList) {
                    // Parse lessons from LLM text and map to tool data
                    const lessonsFromLLM = lessonListMatches.map(line => {
                      const match = line.match(/^(\d+)\.?\s+(.+)$/);
                      if (!match) return null;
                      const [, lessonId, lessonName] = match;
                      // Find lesson in tool data by ID or name
                      const lessonObj = toolLessonList.find(
                        (lesson: any) => lesson.lessonId === lessonId || lesson.lessonName.trim() === lessonName.trim()
                      );
                      return lessonObj || { lessonId, lessonName };
                    }).filter(Boolean);
                    // Find any trailing text after the lesson list
                    const lastMatch = lessonListMatches[lessonListMatches.length - 1];
                    const trailingTextIndex = part.text.indexOf(lastMatch) + lastMatch.length;
                    const trailingText = part.text.slice(trailingTextIndex).trim();
                    return (
                      <div key={key + '-llm-lesson-list'} className="flex flex-col gap-2">
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 my-6">
                          {lessonsFromLLM.map((lesson: any, idx) => (
                            <LessonCard
                              key={`lesson-${lesson.lessonId ?? ''}-${idx}`}
                              {...lesson}
                              className="border border-blue-200 shadow-sm bg-blue-50"
                            />
                          ))}
                        </div>
                        {trailingText && (
                          <div className="mt-2 text-sm text-muted-foreground">
                            <Markdown>{trailingText}</Markdown>
                          </div>
                        )}
                      </div>
                    );
                  }
                  // === LLM LESSON LIST TO CARD VIEW BLOCK END ===

                  // === LLM MCQ JSON CARD VIEW BLOCK START ===
                  function MCQBlock({
                    questions,
                    keyProp,
                    suggestedActions,
                  }: {
                    questions: any[];
                    keyProp: string;
                    suggestedActions?: any[];
                  }) {
                    const [answers, setAnswers] = React.useState<(string | null)[]>(Array(questions.length).fill(null));
                    const [showResults, setShowResults] = React.useState(false);
                    const correctCount = answers.filter((ans, i) => ans === questions[i].correctId).length;
                    const allAnswered = answers.every(ans => ans !== null);
                    const wrongAnswers = questions
                      .map((q, i) => ({
                        question: q.question,
                        correctAnswer: q.choices.find((c: any) => c.id === q.correctId)?.text,
                        userAnswer: answers[i],
                        idx: i
                      }))
                      .filter(({ userAnswer }, i) => userAnswer !== null && userAnswer !== questions[i].correctId);

                    React.useEffect(() => {
                      if (showResults && allAnswered && correctCount < questions.length) {
                        const revisionPrompt = `راجع هذه الأسئلة التي أخطأ فيها الطالب واشرح المفاهيم المتعلقة بها وقدم مراجعة:\n${wrongAnswers.map((w, i) => `${i + 1}. ${w.question} (إجابتك: ${w.userAnswer}, الصحيح: ${w.correctAnswer})`).join('\n')}`;
                        if (typeof window !== 'undefined') {
                          window.dispatchEvent(new CustomEvent('cascade-mcq-revision', { detail: { prompt: revisionPrompt } }));
                        }
                      }
                    }, [showResults, allAnswered, correctCount]);

                    const handleSelect = (idx: number, answerId: string) => {
                      setAnswers(prev => {
                        const next = [...prev];
                        next[idx] = answerId;
                        return next;
                      });
                    };

                    return (
                      <div className="flex flex-col gap-6">
                        {questions.map((mcq, idx) => (
                          <LessonMCQ
                            key={`mcq-${'id' in mcq ? mcq.id : ''}-${'question' in mcq ? mcq.question : ''}-${idx}`}
                            {...mcq}
                            onSelect={answerId => handleSelect(idx, answerId)}
                          />
                        ))}

                        {/* Results and Actions after answering all questions */}
                        {allAnswered && (
                          <div className="mt-6 p-4 bg-blue-50 rounded-xl text-right">
                            <div>
                              <strong>النتيجة:</strong> {correctCount} / {questions.length} إجابات صحيحة
                            </div>
                            {/* If there are mistakes, show review and retry buttons */}
                            {correctCount < questions.length ? (
                              <div className="mt-4 flex flex-col sm:flex-row gap-3 justify-end items-end">
                                <button
                                  className="px-4 py-2 rounded bg-blue-600 text-white font-bold hover:bg-blue-700 transition"
                                  onClick={() => setShowResults(true)}
                                  disabled={showResults}
                                >
                                  غرض المراجعة
                                </button>
                                <button
                                  className="px-4 py-2 rounded bg-gray-200 text-gray-900 font-bold hover:bg-gray-300 transition border border-gray-300"
                                  onClick={() => {
                                    setAnswers(Array(questions.length).fill(null));
                                    setShowResults(false);
                                  }}
                                >
                                  تدرب مرة أخرى
                                </button>
                              </div>
                            ) : (
                              <div className="mt-4 flex flex-col items-end gap-2">
                                <div className="text-green-700 font-semibold">أحسنت! كل الإجابات صحيحة.</div>
                                <div className="flex flex-row gap-3 items-center">
                                  <span>هل ترغب في أسئلة إضافية؟</span>
                                  <button
                                    className="px-4 py-2 rounded bg-primary text-primary-foreground font-semibold border border-border hover:bg-primary/90 transition"
                                    onClick={() => {
                                      window.dispatchEvent(new CustomEvent('cascade-mcq-action', { detail: { action: 'more_mcq', prompt: 'أنشئ لي أسئلة اختيار من متعدد إضافية في نفس الموضوع' } }));
                                    }}
                                  >
                                    نعم
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        )}

                        {/* Show mistakes if showResults is true */}
                        {showResults && wrongAnswers.length > 0 && (
                          <ul className="list-none p-0 m-0">
                            {wrongAnswers.map((w: { question: string; correctAnswer: string; userAnswer: string | null; idx: number }, idx: number) => (
                              <li key={`wrong-${w.idx ?? ''}-${w.question ?? ''}-${idx}`}>
                                <div>
                                  <span className="font-semibold">{w.question}</span>
                                  <br />
                                  إجابتك: <span className="text-red-700">{w.userAnswer}</span>، الصحيح: <span className="text-green-700">{w.correctAnswer}</span>
                                </div>
                              </li>
                            ))}
                          </ul>
                        )}
                        {/* Suggested Actions Block from LLM */}
                        {Array.isArray(suggestedActions) && suggestedActions.length > 0 && (
                          <div className="mt-6 flex flex-col gap-4 items-end">
                            <div className="w-full sm:w-auto bg-background border border-border rounded-xl shadow-sm p-4 flex flex-col gap-3">
                              <div className="flex flex-col sm:flex-row gap-3 justify-end">
                                {suggestedActions.map((action, idx) => (
                                  <button
                                    key={`action-${action.action ?? ''}-${idx}`}
                                    className="flex items-center gap-2 px-4 py-2 rounded bg-primary text-primary-foreground font-semibold border border-border hover:bg-primary/90 transition"
                                    onClick={() => {
                                      window.dispatchEvent(new CustomEvent('cascade-mcq-action', { detail: { action: action.action, prompt: action.prompt || action.label } }));
                                    }}
                                  >
                                    {action.icon && <span role="img" aria-label={action.action}>{action.icon}</span>}
                                    {action.label}
                                  </button>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  }
                  try {
                    const parsed = JSON.parse(part.text.trim());
                    if (parsed && parsed.type === "mcq" && Array.isArray(parsed.questions)) {
                      return <MCQBlock questions={parsed.questions} keyProp={key} suggestedActions={parsed.suggestedActions} />;
                    }
                  } catch {}
                  // === LLM MCQ JSON CARD VIEW BLOCK END ===

                  // === LLM MCQ TO CARD VIEW BLOCK START ===
                  // === LLM LESSON SUMMARY JSON CARD VIEW BLOCK START ===
                  try {
                    const parsed = JSON.parse(part.text.trim());
                    if (parsed && parsed.type === "lesson_summary") {
                      return (
                        <LessonSummary
                          key={key}
                          title={typeof parsed === 'object' && parsed !== null && 'title' in parsed ? String(parsed.title) : 'ملخص الدرس'}
                          summary={typeof parsed === 'object' && parsed !== null && 'summary' in parsed ? String(parsed.summary) : ''}
                          keyPoints={Array.isArray(parsed?.keyPoints) ? parsed.keyPoints : []}
                          vocabulary={Array.isArray(parsed?.vocabulary) ? parsed.vocabulary : []}
                          nextSteps={Array.isArray(parsed?.nextSteps) ? parsed.nextSteps : []}
                        />
                      );
                    }
                  } catch {}
                  // === LLM LESSON SUMMARY JSON CARD VIEW BLOCK END ===

                  // Check for lesson summaries
                  if (part.text.includes('type:lesson_summary')) {
                    try {
                      // Split by type:lesson_summary and filter out empty strings
                      const summaries = part.text.split('type:lesson_summary')
                        .filter(text => text.trim())
                        .map(text => {
                          try {
                            return JSON.parse(text.trim());
                          } catch (e) {
                            console.error('Failed to parse individual summary:', e);
                            return null;
                          }
                        })
                        .filter(summary => summary !== null);

                      if (summaries.length > 0) {
                        return (
                          <div key={key} className="space-y-6">
                            {summaries.map((summary, index) => {
                              // Ensure all required props are present
                              if (!summary || typeof summary !== 'object' || summary === null || !('title' in summary) || !('summary' in summary) || !('keyPoints' in summary)) {
                                console.error('Missing required props in summary:', summary);
                                return null;
                              }
                              return (
                                <LessonSummary
                                  key={`${key}-summary-${index}`}
                                  title={typeof summary === 'object' && summary !== null && 'title' in summary ? String(summary.title) : 'ملخص الدرس'}
                                  summary={typeof summary === 'object' && summary !== null && 'summary' in summary ? String(summary.summary) : ''}
                                  keyPoints={Array.isArray(summary?.keyPoints) ? summary.keyPoints : []}
                                  vocabulary={Array.isArray(summary?.vocabulary) ? summary.vocabulary : []}
                                  nextSteps={Array.isArray(summary?.nextSteps) ? summary.nextSteps : []}
                                />
                              );
                            }).filter(Boolean)}
                          </div>
                        );
                      }
                    } catch (e) {
                      console.error('Failed to process summaries:', e);
                    }
                  }

                  // Check for multiple MCQs with type:mcq tags
                  if (part.text.includes('type:mcq')) {
                    try {
                      // Split by type:mcq and filter out empty strings
                      const mcqs = part.text.split('type:mcq')
                        .filter(text => text.trim())
                        .map(text => {
                          try {
                            return JSON.parse(text.trim());
                          } catch (e) {
                            console.error('Failed to parse individual MCQ:', e);
                            return null;
                          }
                        })
                        .filter(mcq => mcq !== null);

                      if (mcqs.length > 0) {
                        return (
                          <div key={key} className="space-y-4">
                            {mcqs.map((mcq, index) => (
                              <LessonMCQ
                                key={`mcq-${'id' in mcq ? mcq.id : ''}-${'question' in mcq ? mcq.question : ''}-${index}`}
                                {...mcq}
                              />
                            ))}
                          </div>
                        );
                      }
                    } catch (e) {
                      console.error('Failed to process MCQs:', e);
                    }
                  }

                  // Fallback: Simple MCQ block detection
                  const mcqBlockMatch = part.text.match(/سؤال|أسئلة|متعددة الخيارات|MCQ/);
                  if (mcqBlockMatch) {
                    // Split by question number (e.g., 1. ... 2. ...)
                    const mcqQuestions = part.text.split(/\n\s*\d+\./).slice(1);
                    const parsedMcqs = mcqQuestions.map(qBlock => {
                      const lines = qBlock.trim().split(/\n+/);
                      const question = lines[0]?.replace(/\(.+\)/g, '').trim();
                      // Find choices: lines starting with (أ) (ب) (ج) ...
                      const choices = lines.slice(1)
                        .map(line => {
                          const match = line.match(/^\(?([أ-يA-Za-z])\)?\s*[\)|\.]?\s*(.+)$/);
                          return match ? { id: match[1], text: match[2].trim() } : null;
                        })
                        .filter(Boolean);
                      return { question, choices };
                    }).filter(mcq => mcq.choices && mcq.choices.length > 0);
                    if (parsedMcqs.length > 0) {
                      return (
                        <div key={key + '-llm-mcq-list'} className="flex flex-col gap-6">
                          {parsedMcqs.map((mcq, idx) => (
                            <LessonMCQ
                              key={`mcq-${'id' in mcq ? mcq.id : ''}-${'question' in mcq ? mcq.question : ''}-${idx}`}
                              question={'question' in mcq ? mcq.question : 'سؤال'}
                              choices={Array.isArray(mcq.choices) 
                                ? mcq.choices
                                    .filter((c): c is { id: string; text: string } => c !== null && typeof c === 'object' && 'id' in c && 'text' in c)
                                    .map((c, i) => ({ id: String(i), text: c.text }))
                                : []
                              }
                              correctId={'correctId' in mcq ? String(mcq.correctId) : '0'}
                              onSelect={handleMCQSelect}
                            />
                          ))}
                        </div>
                      );
                    }
                  }
                  // === LLM MCQ TO CARD VIEW BLOCK END ===
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      {message.role === 'user' && !isReadonly && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              data-testid="message-edit-button"
                              variant="ghost"
                              className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100"
                              onClick={() => {
                                setMode('edit');
                              }}
                            >
                              <PencilEditIcon />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Edit message</TooltipContent>
                        </Tooltip>
                      )}

                      <div
                        data-testid="message-content"
                        className={cn('flex flex-col gap-4', {
                          'bg-primary text-primary-foreground px-3 py-2 rounded-xl':
                            message.role === 'user',
                        })}
                      >
                        <Markdown>{part.text}</Markdown>
                      </div>
                    </div>
                  );
                }

                if (mode === 'edit') {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      <div className="size-8" />

                      <MessageEditor
                        key={message.id}
                        message={message}
                        setMode={setMode}
                        setMessages={setMessages}
                        reload={reload}
                      />
                    </div>
                  );
                }
              }

              if (type === 'tool-invocation') {
                const { toolInvocation } = part;
                const { toolName, toolCallId, state } = toolInvocation;
                const tool = tools?.[toolName];

                if (state === 'call') {
                  const { args } = toolInvocation;

                  if (tool?.capabilities === 'executable') {
                    // If the tool was allowed to execute before, we don't need to ask for permission again
                    if (
                      data?.approved &&
                      data?.approved?.indexOf(toolName) !== -1
                    ) {
                      // return addToolResult directly cause component rendering conflict
                      setTimeout(() => {
                        startTransition(() => {
                          addToolResult?.({
                            toolCallId,
                            result: APPROVAL.YES,
                          });
                        });
                      }, 100);
                      return;
                    }

                    return (
                      <ToolPermissionRequest
                        key={toolCallId}
                        args={args}
                        toolName={toolName}
                        description={tool?.description ?? ''}
                        onAllowOnceAction={() => {
                          handleApproveResult({
                            toolName,
                            toolCallId,
                            result: APPROVAL.YES,
                          });
                        }}
                        onAllowAlwaysAction={() => {
                          handleApproveResult({
                            toolName,
                            toolCallId,
                            always: true,
                            result: APPROVAL.YES,
                          });
                        }}
                        onDenyAction={() => {
                          handleApproveResult({
                            toolName,
                            toolCallId,
                            result: APPROVAL.NO,
                          });
                        }}
                      />
                    );
                  }

                  return (
                    <div
                      key={toolCallId}
                      className={cx({
                        skeleton: ['getWeather'].includes(toolName),
                      })}
                    >
                      {toolName === 'getWeather' ? (
                        <Weather />
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview isReadonly={isReadonly} args={args} />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolCall
                          type="update"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolCall
                          type="request-suggestions"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : (
                        <ToolContentCall
                          state={state}
                          toolName={toolName}
                          args={args}
                        />
                      )}
                    </div>
                  );
                }

                if (state === 'result') {
                  const { result, args } = toolInvocation;

                  return (
                    <div key={toolCallId}>
                      {toolName === 'getWeather' ? (
                        <Weather weatherAtLocation={result} />
                       ) : toolName === 'curriculumLessonList' ? (
                        <ToolContentCall
                          state={state}
                          toolName={toolName}
                          args={args}
                          result={result}
                          isLoading={isLoading}
                        />
                      ) : toolName === 'curriculumLessonContent' || toolName === 'curriculumLessonSummary' ? (
                        // Render a LessonSummary for lesson content/summary
                        <LessonSummary summary={result?.content || result?.summary || 'لا يوجد ملخص متاح.'} />
                      ) : toolName === 'curriculumLessonQuestions' ? (
                        // Render LessonMCQ for lesson questions (if available)
                        result && Array.isArray(result.questions) && result.questions.length > 0 ? (
                          <div className="mcq-container">
                            <div className="text-sm text-gray-600 mb-2">
                              {result.questions.length} question(s) available
                            </div>
                            {result.questions.map((q: any, idx: number) => {
                              console.log(`[Message] Rendering MCQ ${idx + 1}/${result.questions.length}:`, q);
                              return (
                                <div key={`mcq-${idx}`} className="mb-4">
                                  <LessonMCQ
                                    question={q.question}
                                    choices={q.choices?.map((c: any, cIdx: number) => ({
                                      id: c?.id || String(cIdx),
                                      text: c?.text || String(c)
                                    }))}
                                    correctId={q.correctId}
                                    onSelect={handleMCQSelect}
                                  />
                                </div>
                              );
                            })}
                          </div>
                        ) : (
                          <div className="text-gray-500 italic">لا توجد أسئلة متاحة لهذا الدرس.</div>
                        )
                      ) : toolName === 'createDocument' ? (
                        <DocumentPreview
                          isReadonly={isReadonly}
                          result={result}
                        />
                      ) : toolName === 'updateDocument' ? (
                        <DocumentToolResult
                          type="update"
                          result={result}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'requestSuggestions' ? (
                        <DocumentToolResult
                          type="request-suggestions"
                          result={result}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === 'calculator' ? (
                        <Calculator key={toolCallId} args={args} />
                      ) : toolName === 'pokemons_query' ? (
                        <PokemonCarousel
                          result={result}
                          onClickPokemon={(poketmon) => {
                            setInput?.(
                              () =>
                                `I would like to know more about ${poketmon.name} (${poketmon.id})`,
                            );
                          }}
                        />
                      ) : (
                        <ToolContentCall // @FIXME: change name to ToolContentResult thenm remote state
                          state={state}
                          args={args}
                          result={result}
                          toolName={toolName}
                          isLoading={isLoading}
                        />
                      )}
                    </div>
                  );
                }
              }
            })}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.id !== nextProps.message.id) return false;
    if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;
    if (!equal(prevProps.tools, nextProps.tools)) return false;

    return true;
  },
);

export const ThinkingMessage = () => {
  const role = 'assistant';

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message "
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          'flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl',
          {
            'group-data-[role=user]/message:bg-muted': true,
          },
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border">
          <SparklesIcon size={14} />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            Hmm...
          </div>
        </div>
      </div>
    </motion.div>
  );
};